"""
Mileage Entry schema module.
This module defines the schema for MileageEntry model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.mileage_entry import MileageEntry
from datetime import date

class MileageEntrySchema(ma.SQLAlchemySchema):
    """Schema for MileageEntry model."""

    class Meta:
        """Meta class for MileageEntrySchema."""
        model = MileageEntry
        load_instance = True

    id = ma.auto_field(dump_only=True)
    user_id = fields.Integer(required=True)
    date = fields.Date(required=True)
    license_plate = fields.String(required=True, validate=validate.Length(min=1, max=20))
    start_odometer = fields.Integer(required=True, validate=validate.Range(min=0))
    end_odometer = fields.Integer(required=True, validate=validate.Range(min=0))
    kilometers = fields.Float(required=True)
    reason = fields.String(required=True, validate=validate.Length(min=1, max=255))
    description = fields.String(allow_none=True)
    status = fields.String(dump_only=True)
    approved_by = fields.Integer(allow_none=True, dump_only=True)
    approved_at = fields.DateTime(allow_none=True, dump_only=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    # Additional fields for response
    user_name = fields.String(dump_only=True)
    approver_name = fields.String(dump_only=True)

    @validates('date')
    def validate_date(self, value):
        """Validate date field."""
        if value > date.today():
            raise ValidationError("Date cannot be in the future")

    @validates('kilometers')
    def validate_kilometers(self, value):
        """Validate kilometers field."""
        if value <= 0:
            raise ValidationError("Kilometers must be greater than 0")

    @validates('end_odometer')
    def validate_end_odometer(self, value, **kwargs):
        """Validate that end_odometer is greater than start_odometer."""
        start_odometer = kwargs.get('data', {}).get('start_odometer')
        if start_odometer is not None and value <= start_odometer:
            raise ValidationError("End odometer reading must be greater than start odometer reading")

class MileageEntryCreateSchema(ma.Schema):
    """Schema for creating a mileage entry."""
    user_id = fields.Integer(required=True)
    date = fields.Date(required=True)
    license_plate = fields.String(required=True, validate=validate.Length(min=1, max=20))
    start_odometer = fields.Integer(required=True, validate=validate.Range(min=0))
    end_odometer = fields.Integer(required=True, validate=validate.Range(min=0))
    kilometers = fields.Float(required=True)
    reason = fields.String(required=True, validate=validate.Length(min=1, max=255))
    description = fields.String(allow_none=True)

    @validates('date')
    def validate_date(self, value):
        """Validate date field."""
        if value > date.today():
            raise ValidationError("Date cannot be in the future")

    @validates('kilometers')
    def validate_kilometers(self, value):
        """Validate kilometers field."""
        if value <= 0:
            raise ValidationError("Kilometers must be greater than 0")

    @validates('end_odometer')
    def validate_end_odometer(self, value, **kwargs):
        """Validate that end_odometer is greater than start_odometer."""
        start_odometer = kwargs.get('data', {}).get('start_odometer')
        if start_odometer is not None and value <= start_odometer:
            raise ValidationError("End odometer reading must be greater than start odometer reading")

class MileageEntryUpdateSchema(ma.Schema):
    """Schema for updating a mileage entry."""
    date = fields.Date()
    license_plate = fields.String(validate=validate.Length(min=1, max=20))
    start_odometer = fields.Integer(validate=validate.Range(min=0))
    end_odometer = fields.Integer(validate=validate.Range(min=0))
    kilometers = fields.Float()
    reason = fields.String(validate=validate.Length(min=1, max=255))
    description = fields.String(allow_none=True)

    @validates('date')
    def validate_date(self, value):
        """Validate date field."""
        if value > date.today():
            raise ValidationError("Date cannot be in the future")

    @validates('kilometers')
    def validate_kilometers(self, value):
        """Validate kilometers field."""
        if value <= 0:
            raise ValidationError("Kilometers must be greater than 0")

class MileageEntryApprovalSchema(ma.Schema):
    """Schema for approving or rejecting a mileage entry."""
    status = fields.String(required=True, validate=validate.OneOf(["approved", "rejected"]))
