"""
Main application entry point for production deployment.
This file is used by deployment platforms like Render, Heroku, etc.
"""

from app import create_app
import os

# Create the Flask application
app, _ = create_app()

# Add a health check endpoint for deployment platforms
@app.route('/api/health')
def health_check():
    """Health check endpoint for monitoring services."""
    return {
        'status': 'healthy',
        'message': 'Customer Management API is running'
    }, 200

if __name__ == "__main__":
    # This will only run if the file is executed directly
    # Most deployment platforms will use a WSGI server instead
    port = int(os.getenv("PORT", 5000))
    debug = os.getenv("FLASK_DEBUG", "False").lower() in ("true", "1", "t")
    
    app.run(host="0.0.0.0", port=port, debug=debug)
