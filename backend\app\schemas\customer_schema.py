"""
Customer schema module.
This module defines the schema for Customer model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.customer import Customer, VALID_GENDER_VALUES

class CustomerSchema(ma.SQLAlchemySchema):
    """Schema for Customer model."""

    class Meta:
        """Meta class for CustomerSchema."""
        model = Customer
        load_instance = True

    id = ma.auto_field(dump_only=True)
    date = fields.DateTime(allow_none=True)
    code = fields.String(allow_none=True)
    name = fields.String(required=True)
    kvk_number = fields.String(allow_none=True)
    contact_person = fields.String(allow_none=True)
    gender = fields.String(allow_none=True, validate=validate.OneOf(VALID_GENDER_VALUES + [None, ""]))
    title = fields.String(allow_none=True)
    address = fields.String(allow_none=True)
    postal_code = fields.String(allow_none=True)
    city = fields.String(allow_none=True)
    country = fields.String(allow_none=True)
    address2 = fields.String(allow_none=True)
    postal_code2 = fields.String(allow_none=True)
    city2 = fields.String(allow_none=True)
    country2 = fields.String(allow_none=True)
    phone = fields.String(allow_none=True)
    mobile = fields.String(allow_none=True)
    fax = fields.String(allow_none=True)
    email = fields.Email(allow_none=True)
    invoice_email = fields.Email(allow_none=True)
    reminder_email = fields.Email(allow_none=True)
    website = fields.Url(allow_none=True)
    bank_account = fields.String(allow_none=True)
    giro_account = fields.String(allow_none=True)
    vat_number = fields.String(allow_none=True)
    iban = fields.String(allow_none=True)
    bic = fields.String(allow_none=True)
    sepa_auth_type = fields.String(allow_none=True)
    mandate_reference = fields.String(allow_none=True)
    mandate_date = fields.DateTime(allow_none=True)
    customer_type = fields.String(allow_none=True)
    no_email = fields.Boolean()
    payment_term = fields.Integer(allow_none=True)
    newsletter_groups = fields.String(allow_none=True)
    subscriptions = fields.String(allow_none=True)
    notes = fields.String(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    @validates('name')
    def validate_name(self, name):
        """Validate name field."""
        if not name:
            raise ValidationError('Name is required')
        if len(name) > 100:
            raise ValidationError('Name must be less than 100 characters')

# Initialize schemas
customer_schema = CustomerSchema()
customers_schema = CustomerSchema(many=True)
