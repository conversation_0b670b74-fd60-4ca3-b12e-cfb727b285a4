import React, { useState, useEffect, useRef } from 'react';
import { Event } from '../types/event';
import { handleEventSchema, validateData } from '../utils/validation';
import { useConfirmation } from '../context/ConfirmationContext';

interface HandleEventModalProps {
  event: Event;
  onClose: () => void;
  onCompleteWithFile: (event: Event) => void;
  submitting: boolean;
  file: File | null;
  setFile: (file: File | null) => void;
  newExpiryDate: string;
  setNewExpiryDate: (date: string) => void;
  expiryType: "date" | "niet_van_toepassing";
  setExpiryType: (type: "date" | "niet_van_toepassing") => void;
  documentNotApplicable: boolean;
  setDocumentNotApplicable: (value: boolean) => void;
  useVersionStatus: boolean;
  setUseVersionStatus: (use: boolean) => void;
  versionStatus: "active" | "inactive";
  setVersionStatus: (status: "active" | "inactive") => void;
}

const HandleEventModal: React.FC<HandleEventModalProps> = ({
  event,
  onClose,
  onCompleteWithFile,
  submitting,
  file,
  setFile,
  newExpiryDate,
  setNewExpiryDate,
  expiryType,
  setExpiryType,
  documentNotApplicable,
  setDocumentNotApplicable,
  useVersionStatus,
  setUseVersionStatus,
  versionStatus,
  setVersionStatus
}) => {
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [formModified, setFormModified] = useState(false);
  const { showConfirmation } = useConfirmation();
  const initialStateRef = useRef({
    file,
    newExpiryDate,
    expiryType,
    documentNotApplicable,
    useVersionStatus,
    versionStatus
  });
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setFile(e.target.files[0]);
      setFormModified(true);
    }
  };

  // Track form modifications
  useEffect(() => {
    const currentState = {
      file,
      newExpiryDate,
      expiryType,
      documentNotApplicable,
      useVersionStatus,
      versionStatus
    };

    // Compare current state with initial state
    const hasChanges = JSON.stringify(currentState) !== JSON.stringify(initialStateRef.current);
    setFormModified(hasChanges);
  }, [file, newExpiryDate, expiryType, documentNotApplicable, useVersionStatus, versionStatus]);

  // Handle button clicks
  const handleButtonClick = async (action: string) => {
    if (action === 'complete-with-file') {
      setValidationErrors([]);

      // Validate form data
      const { isValid, errors } = await validateData(handleEventSchema, {
        file,
        expiryType,
        newExpiryDate,
        documentNotApplicable,
        useVersionStatus,
        versionStatus
      });

      if (!isValid) {
        setValidationErrors(errors);
        return;
      }

      // If validation passes, proceed with the action
      if (action === 'complete-with-file') {
        onCompleteWithFile(event);
      }
    } else if (action === 'close') {
      if (formModified) {
        showConfirmation({
          title: "Wijzigingen negeren",
          message: "U heeft niet-opgeslagen wijzigingen. Weet u zeker dat u dit venster wilt sluiten?",
          confirmText: "Negeren",
          cancelText: "Annuleren",
          confirmButtonClass: "bg-red-600 hover:bg-red-700",
          onConfirm: () => onClose()
        });
      } else {
        onClose();
      }
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{pointerEvents: 'auto'}}>
      {/* Dark overlay */}
      <div
        className="absolute inset-0"
        onClick={(e) => {
          e.preventDefault();
          e.stopPropagation();
          handleButtonClick('close');
        }}
        style={{pointerEvents: 'auto', cursor: 'pointer'}}
      ></div>

      {/* Modal content */}
      <div
        className="bg-white dark:bg-dark-secondary rounded-lg shadow-lg p-6 w-full max-w-md relative z-50"
        onClick={(e) => e.stopPropagation()}
        style={{pointerEvents: 'auto'}}
      >
        <h3 className="text-xl font-medium text-amspm-text mb-4">Gebeurtenis Afhandelen {event.id}</h3>
        <div className="text-sm text-gray-500 mb-4">
          Type: {event.event_type}
        </div>

        {validationErrors.length > 0 && (
          <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <ul className="list-disc pl-5">
              {validationErrors.map((err, index) => (
                <li key={index}>{err}</li>
              ))}
            </ul>
          </div>
        )}

        <div className="space-y-4">
          <div className="form-group">
            <label className="block text-amspm-text font-medium mb-1 uppercase">
              Nieuw Document Uploaden ({event.event_type})
            </label>
            <input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="input"
              disabled={submitting || documentNotApplicable}
            />
            <div className="mt-2">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={documentNotApplicable}
                  onChange={(e) => {
                    setDocumentNotApplicable(e.target.checked);
                    if (e.target.checked) setFile(null);
                  }}
                  disabled={submitting}
                  className="form-checkbox"
                />
                <span className="ml-2 text-amspm-text">Document niet van toepassing</span>
              </label>
            </div>
          </div>
          <div className="form-group">
            <label className="block text-amspm-text font-medium mb-1 uppercase">Vervaldatum Type</label>
            <select
              value={expiryType}
              onChange={(e) => {
                setExpiryType(e.target.value as "date" | "niet_van_toepassing");
                if (e.target.value === "niet_van_toepassing") {
                  setNewExpiryDate("");
                }
              }}
              className="input"
              disabled={submitting || documentNotApplicable}
            >
              <option value="date">Datum</option>
              <option value="niet_van_toepassing">Niet van toepassing</option>
            </select>
          </div>
          {expiryType === "date" && !documentNotApplicable && (
            <div className="form-group">
              <label className="block text-amspm-text font-medium mb-1 uppercase">Vervaldatum</label>
              <input
                type="datetime-local"
                value={newExpiryDate}
                onChange={(e) => setNewExpiryDate(e.target.value)}
                className="input"
                disabled={submitting}
              />
            </div>
          )}
          <div className="form-group">
            <div className="mb-2">
              <label className="inline-flex items-center">
                <input
                  type="checkbox"
                  checked={useVersionStatus}
                  onChange={(e) => setUseVersionStatus(e.target.checked)}
                  disabled={submitting}
                  className="form-checkbox"
                />
                <span className="ml-2 text-amspm-text font-medium uppercase">Use Version Status</span>
              </label>
            </div>
            {useVersionStatus && (
              <select
                value={versionStatus}
                onChange={(e) => setVersionStatus(e.target.value as "active" | "inactive")}
                className="input"
                disabled={submitting}
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            )}
          </div>
          <div className="flex space-x-4" style={{position: 'relative', zIndex: 100}}>
            <button
              type="button"
              onClick={() => handleButtonClick('complete-with-file')}
              className="btn btn-secondary flex-1 modal-button"
              disabled={submitting}
              style={{pointerEvents: 'auto'}}
            >
              {submitting
                ? "Bezig..."
                : documentNotApplicable && expiryType === "niet_van_toepassing"
                ? "Gebeurtenis Voltooien"
                : "Voltooien"}
            </button>

          </div>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleButtonClick('close');
            }}
            className="btn btn-danger w-full modal-button"
            disabled={submitting}
            style={{pointerEvents: 'auto', cursor: 'pointer'}}
          >
            Annuleren
          </button>
        </div>
      </div>
    </div>
  );
};

export default HandleEventModal;
