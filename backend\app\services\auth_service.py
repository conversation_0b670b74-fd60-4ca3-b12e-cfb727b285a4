from app.repositories.user_repository import UserRepository
import firebase_admin
from firebase_admin import auth
import logging

logger = logging.getLogger(__name__)

class AuthService:
    def __init__(self):
        self.user_repo = UserRepository()

    def verify_token(self, token: str, ip_address=None, user_agent=None) -> dict:
        try:
            # Verify the Firebase token
            decoded_token = auth.verify_id_token(token)
            user = self.user_repo.get_by_firebase_uid(decoded_token["uid"])

            if not user:
                logger.error(f"User with Firebase UID {decoded_token['uid']} not found in database")
                raise Exception("User not found in database")

            return user.to_dict()
        except auth.ExpiredIdTokenError:
            logger.error("Token has expired")
            raise Exception("Token has expired")
        except auth.InvalidIdTokenError:
            logger.error("Invalid token")
            raise Exception("Invalid token")
        except Exception as e:
            logger.error(f"Token verification failed: {str(e)}")
            raise