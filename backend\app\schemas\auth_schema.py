"""
Auth schema module.
This module defines the schema for authentication-related validation.
"""
from marshmallow import fields, validates, ValidationError
from app.schemas import ma

class TokenVerificationSchema(ma.Schema):
    """Schema for token verification."""

    token = fields.String(required=True)

    @validates('token')
    def validate_token(self, token):
        """Validate token field."""
        if not token:
            raise ValidationError('Token is required')
        if len(token) < 10:
            raise ValidationError('Invalid token format')

class LogoutSchema(ma.Schema):
    """Schema for logout."""

    token = fields.String(required=False)

    @validates('token')
    def validate_token(self, token):
        """Validate token field."""
        if token and len(token) < 10:
            raise ValidationError('Invalid token format')

# Initialize schemas
token_verification_schema = TokenVerificationSchema()
logout_schema = LogoutSchema()
