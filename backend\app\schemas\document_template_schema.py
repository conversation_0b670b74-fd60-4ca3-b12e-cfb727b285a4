"""
Document Template schema module.
This module defines the schema for DocumentTemplate model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.document_template import DocumentTemplate
from app.models.document import ALLOWED_DOCUMENT_TYPES

class DocumentTemplateSchema(ma.SQLAlchemySchema):
    """Schema for DocumentTemplate model."""
    
    class Meta:
        """Meta class for DocumentTemplateSchema."""
        model = DocumentTemplate
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    name = fields.String(required=True)
    document_type = fields.String(required=True, validate=validate.OneOf(ALLOWED_DOCUMENT_TYPES))
    description = fields.String(allow_none=True)
    file_path = fields.String(dump_only=True)
    file_type = fields.String()
    created_by = fields.Integer(required=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)
    
    # Additional fields for template upload
    file = fields.Raw(required=False, load_only=True)
    
    @validates('name')
    def validate_name(self, name):
        """Validate name field."""
        if not name:
            raise ValidationError('Name is required')
        if len(name) > 100:
            raise ValidationError('Name must be less than 100 characters')
    
    @validates('document_type')
    def validate_document_type(self, document_type):
        """Validate document_type field."""
        if not document_type:
            raise ValidationError('Document type is required')
        if document_type not in ALLOWED_DOCUMENT_TYPES:
            raise ValidationError(f'Invalid document type. Must be one of {ALLOWED_DOCUMENT_TYPES}')

# Initialize schemas
document_template_schema = DocumentTemplateSchema()
document_templates_schema = DocumentTemplateSchema(many=True)
