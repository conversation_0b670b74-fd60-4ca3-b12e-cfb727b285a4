import axios from "axios";
import { auth } from "./firebase";

// Function to get CSRF token from cookies
const getCSRFToken = (): string | null => {
  // Flask-SeaSurf uses '_csrf_token' as the default cookie name
  const name = "_csrf_token=";
  const decodedCookie = decodeURIComponent(document.cookie);
  const cookieArray = decodedCookie.split(";");

  for (let i = 0; i < cookieArray.length; i++) {
    let cookie = cookieArray[i].trim();
    if (cookie.indexOf(name) === 0) {
      return cookie.substring(name.length, cookie.length);
    }
  }
  return null;
};

const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL,
  withCredentials: true, // Enable credentials to support CSRF tokens
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 15000, // Default timeout of 15 seconds
});

api.interceptors.request.use(async (config) => {
  // Add Firebase authentication token only if needed (for initial login)
  // For most requests, we'll use the HttpOnly cookie that was set during login
  const user = auth.currentUser;
  if (user && config.url?.includes('/auth/verify')) {
    const token = await user.getIdToken();
    config.headers.Authorization = `Bearer ${token}`;
  }

  // Add CSRF token for non-GET requests
  if (config.method !== 'get') {
    const csrfToken = getCSRFToken();
    if (csrfToken) {
      // Flask-SeaSurf uses 'X-CSRFToken' as the default header name
      config.headers['X-CSRFToken'] = csrfToken;
    }
  }

  return config;
});

api.interceptors.response.use(
  (response) => {
    // Check if any sensitive fields have been sanitized and handle them appropriately
    if (response.data && typeof response.data === 'object') {
      // If we detect sanitized fields (marked with ***), log a message
      const hasSanitizedFields = JSON.stringify(response.data).includes('***');
      if (hasSanitizedFields && process.env.NODE_ENV === 'development') {
        console.warn('Response contains sanitized fields for security reasons.');
      }
    }
    return response;
  },
  (error) => {
    // For 401 unauthorized errors, redirect to login
    if (error.response?.status === 401) {
      auth.signOut();
      window.location.href = '/login';
    }
    // For 403 forbidden errors, redirect to dashboard instead of logging out
    else if (error.response?.status === 403) {
      // Check if we're already on the dashboard to prevent redirect loops
      if (!window.location.pathname.includes('/user-dashboard')) {
        window.location.href = '/user-dashboard';
      }
    }
    return Promise.reject(error);
  }
);

export default api;