import { Document } from "../types/document";

export const getStatusColor = (expiry_status: string): string => {
  switch (expiry_status) {
    case "red":
      return "text-red-600";
    case "orange":
      return "text-orange-500";
    case "green":
      return "text-green-600";
    default:
      return "text-gray-600";
  }
};

export const isDocumentActive = (document: Document): boolean => {
  return document.status === "active";
};

export const needsAttention = (document: Document): boolean => {
  return document.status === "active" && 
         (document.expiry_status === "red" || document.expiry_status === "orange");
};
