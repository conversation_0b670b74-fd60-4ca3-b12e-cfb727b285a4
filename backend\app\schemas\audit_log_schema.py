"""
Audit Log schema module.
This module defines the schema for AuditLog model validation.
"""
from marshmallow import fields, validates, ValidationError
from app.schemas import ma
from app.models.audit_log import AuditLog

class AuditLogSchema(ma.SQLAlchemySchema):
    """Schema for AuditLog model."""
    
    class Meta:
        """Meta class for AuditLogSchema."""
        model = AuditLog
        load_instance = True
    
    id = ma.auto_field(dump_only=True)
    user_id = fields.Integer(allow_none=True)
    action = fields.String(required=True)
    entity_type = fields.String(required=True)
    entity_id = fields.Integer(allow_none=True)
    details = fields.String(allow_none=True)
    ip_address = fields.String(allow_none=True)
    user_agent = fields.String(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    
    # Additional fields for response
    user_email = fields.String(dump_only=True)
    
    @validates('action')
    def validate_action(self, action):
        """Validate action field."""
        if not action:
            raise ValidationError('Action is required')
    
    @validates('entity_type')
    def validate_entity_type(self, entity_type):
        """Validate entity_type field."""
        if not entity_type:
            raise ValidationError('Entity type is required')

# Initialize schemas
audit_log_schema = AuditLogSchema()
audit_logs_schema = AuditLogSchema(many=True)
