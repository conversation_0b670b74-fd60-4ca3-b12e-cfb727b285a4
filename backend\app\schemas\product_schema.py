"""
Product schema module.
This module defines the schema for Product model validation.
"""
from marshmallow import fields, validates, ValidationError, validate
from app.schemas import ma
from app.models.product import Product

class ProductSchema(ma.SQLAlchemySchema):
    """Schema for Product model."""

    class Meta:
        """Meta class for ProductSchema."""
        model = Product
        load_instance = True

    id = ma.auto_field(dump_only=True)
    product_code = fields.String(allow_none=True)
    name = fields.String(required=True)
    description = fields.String(allow_none=True)
    gross_price = fields.Float(allow_none=True)
    discount_percentage = fields.Float(allow_none=True)
    net_price = fields.Float(allow_none=True)
    category = fields.String(allow_none=True)
    subcategory = fields.String(allow_none=True)
    info = fields.String(allow_none=True)
    created_at = ma.auto_field(dump_only=True)
    updated_at = ma.auto_field(dump_only=True)

    @validates('name')
    def validate_name(self, name):
        """Validate name field."""
        if not name:
            raise ValidationError('Product name is required')
        if len(name) > 255:
            raise ValidationError('Product name must be less than 255 characters')

    @validates('gross_price')
    def validate_gross_price(self, gross_price):
        """Validate gross_price field."""
        if gross_price is not None and gross_price < 0:
            raise ValidationError('Gross price cannot be negative')

    @validates('discount_percentage')
    def validate_discount_percentage(self, discount_percentage):
        """Validate discount_percentage field."""
        if discount_percentage is not None and (discount_percentage < 0 or discount_percentage > 100):
            raise ValidationError('Discount percentage must be between 0 and 100')

    @validates('net_price')
    def validate_net_price(self, net_price):
        """Validate net_price field."""
        if net_price is not None and net_price < 0:
            raise ValidationError('Net price cannot be negative')


# Create instances of the schema for single and multiple products
product_schema = ProductSchema()
products_schema = ProductSchema(many=True)
